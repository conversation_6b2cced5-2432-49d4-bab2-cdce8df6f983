# 大纲功能测试文档

这个文档用于测试大纲功能在不同编辑模式下的跳转能力。

## 第一级标题测试

这里是第一级标题下的内容。大纲应该能够正确跳转到这里。

### 第二级标题 A

这是一个二级标题，包含一些测试内容。

#### 第三级标题 A.1

这是一个三级标题，测试深层级的跳转功能。

##### 第四级标题 A.1.1

这是一个四级标题，测试更深层级的跳转。

###### 第五级标题 A.1.1.1

这是一个五级标题，测试最深层级的跳转。

#### 第三级标题 A.2

另一个三级标题，用于测试同级别标题的跳转。

### 第二级标题 B

这是另一个二级标题。

## 功能特性测试

让我们测试一些特殊的标题格式。

### 包含中文和English的标题

这个标题包含中英文混合，测试ID生成功能。

### 包含特殊字符！@#$%的标题

这个标题包含特殊字符，测试ID生成的鲁棒性。

### 数字标题 123456

这个标题包含数字，测试数字处理。

## 编辑模式测试指南

请在不同的编辑模式下测试大纲跳转功能：

### 源码模式 (Source Mode)

- 点击大纲中的任意标题
- 编辑器应该跳转到对应的行
- 光标应该定位到标题行

### 预览模式 (Preview Mode)

- 点击大纲中的任意标题
- 预览区域应该滚动到对应的标题
- 标题应该在视窗中居中显示

### 分屏模式 (Split Mode)

- 点击大纲中的任意标题
- 源码侧应该跳转到对应行
- 预览侧应该滚动到对应标题
- 两侧应该同步跳转

### 实时模式 (Live Mode)

- 点击大纲中的任意标题
- 应该滚动到包含该标题的段落
- 段落应该在视窗中突出显示

## 长内容测试

为了测试滚动功能，这里添加一些长内容。

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

### 中间位置的标题

这个标题位于文档中间，用于测试长文档中的跳转。

Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.

### 另一个测试标题

继续测试长文档的跳转功能。

Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur.

At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.

## 测试结果

请在每种模式下测试大纲功能，并验证：

1. ✅ **点击响应** - 点击大纲项是否有响应
2. ✅ **跳转准确性** - 是否跳转到正确的位置
3. ✅ **滚动动画** - 是否有平滑的滚动动画
4. ✅ **视觉反馈** - 是否有合适的视觉反馈
5. ✅ **边界情况** - 文档开头、结尾的标题是否正常

### 文档末尾标题

这是最后一个标题，用于测试跳转到文档末尾的情况。

---

**测试完成！** 🎉

如果所有功能都正常工作，说明大纲功能已经成功实现并支持所有编辑模式。 