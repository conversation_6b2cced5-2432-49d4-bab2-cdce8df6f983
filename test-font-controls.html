<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字号控制测试 - 暗色模式</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        background: 'hsl(var(--background))',
                        foreground: 'hsl(var(--foreground))',
                        primary: 'hsl(var(--primary))',
                        secondary: 'hsl(var(--secondary))',
                        border: 'hsl(var(--border))',
                        ring: 'hsl(var(--ring))',
                        muted: 'hsl(var(--muted))',
                        'muted-foreground': 'hsl(var(--muted-foreground))',
                        accent: 'hsl(var(--accent))'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 0 0% 3.9%;
            --primary: 0 0% 9%;
            --secondary: 0 0% 96.1%;
            --border: 0 0% 89.8%;
            --ring: 0 0% 3.9%;
            --muted: 0 0% 96.1%;
            --muted-foreground: 0 0% 45.1%;
            --accent: 0 0% 96.1%;
            --editor-font-size: 16px;
            --editor-line-height: 1.6;
            --editor-paragraph-spacing: 1.5em;
        }
        .dark {
            --background: 0 0% 3.9%;
            --foreground: 0 0% 98%;
            --primary: 0 0% 98%;
            --secondary: 0 0% 14.9%;
            --border: 0 0% 14.9%;
            --ring: 0 0% 83.1%;
            --muted: 0 0% 14.9%;
            --muted-foreground: 0 0% 63.9%;
            --accent: 0 0% 14.9%;
        }
        
        /* 改进的滑块样式 */
        .slider-container {
            position: relative;
            width: 100%;
            height: 20px;
            display: flex;
            align-items: center;
        }
        
        .slider-track {
            position: relative;
            height: 8px;
            width: 100%;
            overflow: hidden;
            border-radius: 9999px;
            background-color: hsl(var(--secondary));
            cursor: pointer;
        }
        
        .slider-range {
            position: absolute;
            height: 100%;
            background-color: hsl(var(--primary));
            border-radius: 9999px;
            transition: width 0.2s ease;
        }
        
        .slider-thumb {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid hsl(var(--primary));
            background-color: hsl(var(--background));
            cursor: grab;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            top: 50%;
            transform: translate(-50%, -50%);
        }
        
        .slider-thumb:hover {
            background-color: hsl(var(--accent));
            transform: translate(-50%, -50%) scale(1.1);
        }
        
        .slider-thumb:active {
            cursor: grabbing;
            transform: translate(-50%, -50%) scale(1.05);
        }
        
        .dark .slider-thumb {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        /* 编辑器样式应用 */
        .editor-preview {
            font-size: var(--editor-font-size);
            line-height: var(--editor-line-height);
        }
        
        .editor-preview p {
            margin-bottom: var(--editor-paragraph-spacing);
        }
    </style>
</head>
<body class="bg-background text-foreground transition-colors min-h-screen">
    <div class="flex h-screen">
        <!-- 左侧内容区域 -->
        <div class="flex-1 p-8">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center justify-between mb-8">
                    <h1 class="text-3xl font-bold">字号控制测试</h1>
                    <button 
                        id="themeToggle"
                        class="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:opacity-90 transition-opacity"
                        onclick="toggleTheme()"
                    >
                        切换主题
                    </button>
                </div>
                
                <div class="editor-preview space-y-4 p-6 border border-border rounded-lg">
                    <h2 class="text-2xl font-semibold">示例文档</h2>
                    <p>这是一个测试段落，用于展示字体大小和行高的效果。您可以通过右侧的控制面板调整字体大小、行高和段落间距。</p>
                    <p>这是第二个段落，用于测试段落间距的效果。当您调整段落间距时，这两个段落之间的距离会发生变化。</p>
                    <h3 class="text-xl font-medium">子标题示例</h3>
                    <p>这是在子标题下的段落内容。字体设置会影响整个文档的可读性和视觉效果。</p>
                    <ul class="list-disc list-inside space-y-2">
                        <li>列表项目一</li>
                        <li>列表项目二</li>
                        <li>列表项目三</li>
                    </ul>
                </div>
                
                <div class="mt-6 text-sm text-muted-foreground">
                    <p>当前主题: <span id="currentTheme">浅色</span></p>
                    <p>当前字体大小: <span id="currentFontSize">16px</span></p>
                    <p>当前行高: <span id="currentLineHeight">1.6</span></p>
                    <p>当前段落间距: <span id="currentParagraphSpacing">1.5em</span></p>
                </div>
            </div>
        </div>
        
        <!-- 右侧控制面板 -->
        <div class="w-80 bg-background border-l border-border p-6">
            <h3 class="text-lg font-semibold mb-6">字体控制</h3>
            
            <div class="space-y-6">
                <!-- 字体大小控制 -->
                <div class="space-y-3">
                    <label class="text-sm font-medium">字体大小</label>
                    <div class="slider-container">
                        <div class="slider-track" onclick="handleSliderClick(event, 'fontSize')">
                            <div id="fontSizeRange" class="slider-range" style="width: 33.33%;"></div>
                        </div>
                        <div id="fontSizeThumb" class="slider-thumb" style="left: 33.33%;"></div>
                    </div>
                    <div class="flex justify-between text-xs text-muted-foreground">
                        <span>12px</span>
                        <span id="fontSizeValue" class="font-medium text-foreground">16px</span>
                        <span>24px</span>
                    </div>
                </div>
                
                <!-- 行高控制 -->
                <div class="space-y-3">
                    <label class="text-sm font-medium">行高</label>
                    <div class="slider-container">
                        <div class="slider-track" onclick="handleSliderClick(event, 'lineHeight')">
                            <div id="lineHeightRange" class="slider-range" style="width: 40%;"></div>
                        </div>
                        <div id="lineHeightThumb" class="slider-thumb" style="left: 40%;"></div>
                    </div>
                    <div class="flex justify-between text-xs text-muted-foreground">
                        <span>1.0</span>
                        <span id="lineHeightValue" class="font-medium text-foreground">1.6</span>
                        <span>2.5</span>
                    </div>
                </div>
                
                <!-- 段落间距控制 -->
                <div class="space-y-3">
                    <label class="text-sm font-medium">段落间距</label>
                    <div class="slider-container">
                        <div class="slider-track" onclick="handleSliderClick(event, 'paragraphSpacing')">
                            <div id="paragraphSpacingRange" class="slider-range" style="width: 40%;"></div>
                        </div>
                        <div id="paragraphSpacingThumb" class="slider-thumb" style="left: 40%;"></div>
                    </div>
                    <div class="flex justify-between text-xs text-muted-foreground">
                        <span>0.5em</span>
                        <span id="paragraphSpacingValue" class="font-medium text-foreground">1.5em</span>
                        <span>3.0em</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let fontSize = 16;
        let lineHeight = 1.6;
        let paragraphSpacing = 1.5;
        
        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            if (isDark) {
                html.classList.remove('dark');
                document.getElementById('currentTheme').textContent = '浅色';
            } else {
                html.classList.add('dark');
                document.getElementById('currentTheme').textContent = '暗色';
            }
        }
        
        function updateFontSize(value) {
            fontSize = value;
            document.documentElement.style.setProperty('--editor-font-size', `${value}px`);
            document.getElementById('fontSizeValue').textContent = `${value}px`;
            document.getElementById('currentFontSize').textContent = `${value}px`;
            
            const percentage = ((value - 12) / (24 - 12)) * 100;
            document.getElementById('fontSizeRange').style.width = `${percentage}%`;
            document.getElementById('fontSizeThumb').style.left = `${percentage}%`;
        }
        
        function updateLineHeight(value) {
            lineHeight = value;
            document.documentElement.style.setProperty('--editor-line-height', value.toString());
            document.getElementById('lineHeightValue').textContent = value.toFixed(1);
            document.getElementById('currentLineHeight').textContent = value.toFixed(1);
            
            const percentage = ((value - 1.0) / (2.5 - 1.0)) * 100;
            document.getElementById('lineHeightRange').style.width = `${percentage}%`;
            document.getElementById('lineHeightThumb').style.left = `${percentage}%`;
        }
        
        function updateParagraphSpacing(value) {
            paragraphSpacing = value;
            document.documentElement.style.setProperty('--editor-paragraph-spacing', `${value}em`);
            document.getElementById('paragraphSpacingValue').textContent = `${value.toFixed(1)}em`;
            document.getElementById('currentParagraphSpacing').textContent = `${value.toFixed(1)}em`;
            
            const percentage = ((value - 0.5) / (3.0 - 0.5)) * 100;
            document.getElementById('paragraphSpacingRange').style.width = `${percentage}%`;
            document.getElementById('paragraphSpacingThumb').style.left = `${percentage}%`;
        }
        
        function handleSliderClick(event, type) {
            const rect = event.currentTarget.getBoundingClientRect();
            const percentage = (event.clientX - rect.left) / rect.width;
            
            if (type === 'fontSize') {
                const value = Math.round(12 + (24 - 12) * percentage);
                updateFontSize(Math.max(12, Math.min(24, value)));
            } else if (type === 'lineHeight') {
                const value = 1.0 + (2.5 - 1.0) * percentage;
                updateLineHeight(Math.max(1.0, Math.min(2.5, Math.round(value * 10) / 10)));
            } else if (type === 'paragraphSpacing') {
                const value = 0.5 + (3.0 - 0.5) * percentage;
                updateParagraphSpacing(Math.max(0.5, Math.min(3.0, Math.round(value * 10) / 10)));
            }
        }
        
        // 初始化
        document.getElementById('currentTheme').textContent = 
            document.documentElement.classList.contains('dark') ? '暗色' : '浅色';
    </script>
</body>
</html>
