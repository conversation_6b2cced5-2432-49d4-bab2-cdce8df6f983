'use client';

import React, { useState, useEffect } from 'react';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';

interface FontSizeControlProps {
  onFontSizeChange?: (fontSize: number) => void;
  onLineHeightChange?: (lineHeight: number) => void;
  onParagraphSpacingChange?: (spacing: number) => void;
}

export function FontSizeControl({
  onFontSizeChange,
  onLineHeightChange,
  onParagraphSpacingChange
}: FontSizeControlProps) {
  const [fontSize, setFontSize] = useState(16);
  const [lineHeight, setLineHeight] = useState(1.6);
  const [paragraphSpacing, setParagraphSpacing] = useState(1.5);

  // 从localStorage加载设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedFontSize = localStorage.getItem('editor-font-size');
      const savedLineHeight = localStorage.getItem('editor-line-height');
      const savedParagraphSpacing = localStorage.getItem('editor-paragraph-spacing');

      if (savedFontSize) {
        const size = parseInt(savedFontSize);
        setFontSize(size);
        onFontSizeChange?.(size);
      }

      if (savedLineHeight) {
        const height = parseFloat(savedLineHeight);
        setLineHeight(height);
        onLineHeightChange?.(height);
      }

      if (savedParagraphSpacing) {
        const spacing = parseFloat(savedParagraphSpacing);
        setParagraphSpacing(spacing);
        onParagraphSpacingChange?.(spacing);
      }
    }
  }, [onFontSizeChange, onLineHeightChange, onParagraphSpacingChange]);

  // 应用字体大小到CSS变量
  useEffect(() => {
    if (typeof window !== 'undefined') {
      document.documentElement.style.setProperty('--editor-font-size', `${fontSize}px`);
      document.documentElement.style.setProperty('--editor-line-height', lineHeight.toString());
      document.documentElement.style.setProperty('--editor-paragraph-spacing', `${paragraphSpacing}em`);
    }
  }, [fontSize, lineHeight, paragraphSpacing]);

  const handleFontSizeChange = (value: number[]) => {
    const newSize = value[0];
    setFontSize(newSize);
    localStorage.setItem('editor-font-size', newSize.toString());
    onFontSizeChange?.(newSize);
  };

  const handleLineHeightChange = (value: number[]) => {
    const newHeight = value[0];
    setLineHeight(newHeight);
    localStorage.setItem('editor-line-height', newHeight.toString());
    onLineHeightChange?.(newHeight);
  };

  const handleParagraphSpacingChange = (value: number[]) => {
    const newSpacing = value[0];
    setParagraphSpacing(newSpacing);
    localStorage.setItem('editor-paragraph-spacing', newSpacing.toString());
    onParagraphSpacingChange?.(newSpacing);
  };

  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label className="text-xs font-medium">字体大小</Label>
        <div className="px-1">
          <Slider
            value={[fontSize]}
            onValueChange={handleFontSizeChange}
            max={24}
            min={12}
            step={1}
            className="w-full"
          />
        </div>
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>12px</span>
          <span className="font-medium text-foreground">{fontSize}px</span>
          <span>24px</span>
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-xs font-medium">行高</Label>
        <div className="px-1">
          <Slider
            value={[lineHeight]}
            onValueChange={handleLineHeightChange}
            max={2.5}
            min={1.0}
            step={0.1}
            className="w-full"
          />
        </div>
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>1.0</span>
          <span className="font-medium text-foreground">{lineHeight.toFixed(1)}</span>
          <span>2.5</span>
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-xs font-medium">段落间距</Label>
        <div className="px-1">
          <Slider
            value={[paragraphSpacing]}
            onValueChange={handleParagraphSpacingChange}
            max={3}
            min={0.5}
            step={0.1}
            className="w-full"
          />
        </div>
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>0.5em</span>
          <span className="font-medium text-foreground">{paragraphSpacing.toFixed(1)}em</span>
          <span>3.0em</span>
        </div>
      </div>
    </div>
  );
}
