<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slider Dark Mode Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        background: 'hsl(var(--background))',
                        foreground: 'hsl(var(--foreground))',
                        primary: 'hsl(var(--primary))',
                        secondary: 'hsl(var(--secondary))',
                        border: 'hsl(var(--border))',
                        ring: 'hsl(var(--ring))'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 0 0% 3.9%;
            --primary: 0 0% 9%;
            --secondary: 0 0% 96.1%;
            --border: 0 0% 89.8%;
            --ring: 0 0% 3.9%;
        }
        .dark {
            --background: 0 0% 3.9%;
            --foreground: 0 0% 98%;
            --primary: 0 0% 98%;
            --secondary: 0 0% 14.9%;
            --border: 0 0% 14.9%;
            --ring: 0 0% 83.1%;
        }
        
        /* Slider styles */
        .slider-track {
            position: relative;
            height: 8px;
            width: 100%;
            overflow: hidden;
            border-radius: 9999px;
            background-color: hsl(var(--secondary));
        }
        
        .slider-range {
            position: absolute;
            height: 100%;
            background-color: hsl(var(--primary));
        }
        
        .slider-thumb {
            display: block;
            height: 20px;
            width: 20px;
            border-radius: 9999px;
            border: 2px solid hsl(var(--primary));
            background-color: hsl(var(--background));
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .slider-thumb:focus {
            outline: none;
            box-shadow: 0 0 0 2px hsl(var(--ring));
        }
    </style>
</head>
<body class="bg-background text-foreground transition-colors">
    <div class="min-h-screen p-8">
        <div class="max-w-md mx-auto space-y-8">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold">Slider Dark Mode Test</h1>
                <button 
                    id="themeToggle"
                    class="px-4 py-2 bg-primary text-primary-foreground rounded-md"
                    onclick="toggleTheme()"
                >
                    切换主题
                </button>
            </div>
            
            <div class="space-y-6 p-6 border border-border rounded-lg">
                <div class="space-y-2">
                    <label class="text-sm font-medium">字体大小</label>
                    <div class="relative">
                        <div class="slider-track">
                            <div class="slider-range" style="width: 50%;"></div>
                        </div>
                        <div class="slider-thumb" style="position: absolute; left: calc(50% - 10px); top: -6px;"></div>
                    </div>
                    <div class="text-xs text-muted-foreground">16px</div>
                </div>
                
                <div class="space-y-2">
                    <label class="text-sm font-medium">行高</label>
                    <div class="relative">
                        <div class="slider-track">
                            <div class="slider-range" style="width: 60%;"></div>
                        </div>
                        <div class="slider-thumb" style="position: absolute; left: calc(60% - 10px); top: -6px;"></div>
                    </div>
                    <div class="text-xs text-muted-foreground">1.6</div>
                </div>
            </div>
            
            <div class="text-sm text-muted-foreground">
                <p>当前主题: <span id="currentTheme">浅色</span></p>
                <p>测试滑块在暗色模式下的显示效果</p>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            if (isDark) {
                html.classList.remove('dark');
                document.getElementById('currentTheme').textContent = '浅色';
            } else {
                html.classList.add('dark');
                document.getElementById('currentTheme').textContent = '暗色';
            }
        }
        
        // 初始化主题
        document.getElementById('currentTheme').textContent = 
            document.documentElement.classList.contains('dark') ? '暗色' : '浅色';
    </script>
</body>
</html>
