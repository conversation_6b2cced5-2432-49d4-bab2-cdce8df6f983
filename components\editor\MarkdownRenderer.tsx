'use client';

import React, { useMemo, useRef, useEffect } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

export default function MarkdownRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  // 监听跳转到预览标题的事件
  useEffect(() => {
    const handleScrollToPreviewHeading = (event: CustomEvent<{ headingId: string }>) => {
      if (containerRef.current) {
        const headingElement = containerRef.current.querySelector(`#${event.detail.headingId}`);
        if (headingElement) {
          headingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    };
  }, []);
  
  const htmlContent = useMemo(() => {
    try {
      const processor = unified()
        .use(remarkParse)
        .use(remarkGfm)
        .use(remarkRehype, { allowDangerousHtml: true })
        .use(rehypeRaw)
        .use(rehypeHighlight)
        .use(rehypeStringify);

      let result = processor.processSync(content);
      let htmlString = String(result);
      
      // 后处理：为标题添加ID
      htmlString = htmlString.replace(
        /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
        (match, tag, text) => {
          const id = text.toLowerCase()
            .replace(/[^\w\u4e00-\u9fa5\s-]/g, '') // 保留中文、英文、数字、空格、连字符
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
          
          if (id) {
            return `<${tag} id="${id}">${text}</${tag}>`;
          }
          return match;
        }
      );
      
      return htmlString;
    } catch (error) {
      console.error('Markdown rendering error:', error);
      return '<p>渲染错误</p>';
    }
  }, [content]);

  // 恢复滚动位置
  useEffect(() => {
    if (containerRef.current && initialScrollPosition !== undefined) {
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = initialScrollPosition;
        }
      }, 100);
    }
  }, [initialScrollPosition]);

  // 监听滚动事件
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onScrollPositionChange) return;

    const handleScroll = () => {
      onScrollPositionChange(container.scrollTop);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onScrollPositionChange]);

  return (
    <div
      ref={containerRef}
      className={`prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto h-full ${className}`}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
      style={{
        lineHeight: '1.7',
        fontSize: '16px'
      }}
    />
  );
}
